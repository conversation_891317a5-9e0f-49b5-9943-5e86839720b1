<?php

namespace App\Models;

use App\Enums\InvoiceStatus;
use App\Models\Concerns\HasJournalEntry;
use App\Models\Contracts\JournalTransaction;
use App\Models\Finance\CashAccount;
use App\Models\Finance\JournalEntry;
use App\Models\Finance\PurchaseOrder;
use App\Models\Traits\DefaultLogOptions;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Support\Facades\DB;
use Spatie\Activitylog\Traits\LogsActivity;

class Bill extends Model implements JournalTransaction
{
    use DefaultLogOptions;
    use HasFactory;
    use HasJournalEntry;
    use LogsActivity;

    protected $fillable = [
        'order_id',

        'bill_date',
        'bill_number',

        'vendor_id',
        'group_id', // @deprecated

        'due_date',

        'currency_code',
        'exchange_rate',

        'subject',
        'notes',

        'total',
        'paid',

        'status',
        'cancellation_note',
    ];

    protected $casts = [
        'bill_date' => 'datetime:Y-m-d',
        'due_date' => 'datetime:Y-m-d',
        'exchange_rate' => 'float',
        'total' => 'float',
        'paid' => 'float',
        'status' => InvoiceStatus::class,
    ];

    public function getTransactionType(): string
    {
        return 'Bill';
    }

    public function getTransactionNumber(): string
    {
        return $this->bill_number;
    }

    protected static function booted()
    {
        static::creating(function (self $model) {
            $model->bill_number ??= self::getNextNumber();
        });
        static::saving(function (self $model) {
            $model->total = $model->getTotal();
            $model->paid = $model->getTotalPayments();
            $model->status = $model->getStatus();
        });
        static::saved(function (self $model) {
            $model->syncJournalEntry();
        });
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(PurchaseOrder::class);
    }

    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * @deprecated
     */
    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(BillItem::class, 'bill_id')->orderBy('order_column');
    }

    public function groups(): HasManyThrough
    {
        return $this->hasManyThrough(Group::class, BillItem::class, 'bill_id', 'id', secondLocalKey: 'group_id')->distinct();
    }

    public function payments(): HasMany
    {
        return $this->hasMany(BillPayment::class, 'bill_id');
    }

    public function getTransactionDetailAttribute(): ?string
    {
        return $this->subject;
    }

    public function getTotal(): float
    {
        return $this->items()->sum(DB::raw('quantity * unit_price * (1 + vat/100)'));
    }

    public function getTotalPayments(): float
    {
        return $this->payments()->sum('amount');
    }

    public function getStatus(): InvoiceStatus
    {
        if ($this->status == InvoiceStatus::Cancelled) {
            return InvoiceStatus::Cancelled;
        }

        return match (true) {
            $this->total > 0 && $this->paid >= $this->total => InvoiceStatus::Paid,
            $this->paid > 0 => InvoiceStatus::PaidPartial,
            default => InvoiceStatus::Unpaid
        };
    }

    public function syncJournalEntry(): void
    {
        /** @var JournalEntry $entry */
        $entry = $this->journal_entry()->updateOrCreate([], [
            'entry_date' => $this->bill_date,
            'details' => "Bill {$this->bill_number}",
        ]);

        $entry->items()->delete();

        $defaultCostAccountId = CashAccount::query()
            ->where('code', config('finance.coa.costs')) // hpp
            ->value('id');

        foreach ($this->items as $item) {
            $costAccountId = $item->product?->category?->cost_account_id;

            $subtotal = $item->quantity * $item->unit_price * (1 + $item->vat / 100);
            $entry->items()->create([
                'type' => 'd',
                'account_id' => $costAccountId ?? $defaultCostAccountId,
                'amount' => $subtotal * $this->exchange_rate,
            ]);
        }

        $entry->items()->create([
            'type' => 'c',
            'account_id' => CashAccount::query()
                ->where('code', config('finance.coa.payable')) // utang usaha
                ->value('id'),
            'amount' => $this->total * $this->exchange_rate,
        ]);
    }

    public function delete()
    {
        $this->journal_entry?->delete();

        return parent::delete();
    }

    public static function getNextNumber(): string
    {
        $period = current_period();
        $periodYear = preg_replace('/[^0-9]/', '', $period->name);

        $prefix = "BI-{$periodYear}";

        $lastBill = static::query()
            ->where('bill_number', 'like', $prefix . '%')
            ->orderBy('bill_number', 'desc')
            ->first();
        $lastNumber = $lastBill ?
        (int) str_replace($prefix, '', $lastBill->bill_number)
            : 0;

        return $prefix . str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
    }
}
