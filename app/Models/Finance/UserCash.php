<?php

namespace App\Models\Finance;

use App\Enums\ExpenseGroup;
use App\Models\Bill;
use App\Models\Contracts\JournalTransaction;
use App\Models\Group;
use App\Models\Traits\DefaultLogOptions;
use App\Models\Traits\WithUserstamps;
use App\Models\User;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\Relation;
use Spatie\Activitylog\Traits\LogsActivity;

class UserCash extends Model implements JournalTransaction
{
    use DefaultLogOptions;
    use HasFactory;
    use LogsActivity;
    use WithUserstamps;

    const CURRENCIES = [
        'SAR' => 'SAR',
        'IDR' => 'IDR',
    ];

    protected $fillable = [
        'user_id',

        'group_id',
        'category_id',

        'cashed_at',

        'type', // d / c
        'amount',
        'currency',
        'exchange_rate',

        'details',
        'attachment',

        'is_fixed', // currently not used

        'related_type',
        'related_id',

        'created_by_id',
        'updated_by_id',

        'verified_at',
        'verified_by_id',
    ];

    protected $casts = [
        'cashed_at' => 'datetime',
        'is_fixed' => 'bool',
    ];

    public function getTransactionType(): string
    {
        return 'Advance Cash';
    }

    public function getTransactionNumber(): string
    {
        return '#' . str_pad($this->id, 6, '0', STR_PAD_LEFT);
    }

    public function getTransactionDetailAttribute(): ?string
    {
        return $this->details;
    }

    protected static function boot()
    {
        static::creating(function (self $model) {
            $model->user_id ??= auth()->id();
            $model->exchange_rate ??= 1;
        });
        static::saved(function (self $model) {
            $model->syncRelated();
            $model->syncJournalEntry();
        });
        static::updating(function (self $model) {
            if ($model->isDirty('related_id') || $model->isDirty('related_type')) {
                if (Relation::getMorphedModel($model->related_type) === Bill::class) {
                    $related_id = $model->getOriginal('related_id');
                    if ($related_id) {
                        Bill::find($related_id)?->payments()->where('user_cash_id', $model->id)->delete();
                    }
                }
            }
        });

        parent::boot();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(CashCategory::class);
    }

    public function journal_entry(): MorphOne
    {
        return $this->morphOne(JournalEntry::class, 'transaction');
    }

    public function related(): MorphTo
    {
        return $this->morphTo();
    }

    public function verifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function syncRelated()
    {
        if ($this->related) {
            switch (get_class($this->related)) {
                case Bill::class:
                    $billCurrency = $this->related->currency_code;
                    $paymentCurrency = $this->currency;
                    if ($billCurrency == 'SAR' && $paymentCurrency == 'IDR') {
                        $amount = $this->amount * $this->exchange_rate;
                        $exchangeRate = 1;
                    } elseif ($billCurrency == 'IDR' && $paymentCurrency == 'SAR') {
                        $amount = $this->amount * $this->related->exchange_rate;
                        $exchangeRate = $this->related->exchange_rate;
                    } else {
                        $amount = $this->amount;
                        $exchangeRate = $this->exchange_rate;
                    }
                    $this->related->payments()->updateOrCreate([
                        'user_cash_id' => $this->id,
                    ], [
                        'description' => $this->details,
                        'attachment' => $this->attachment,
                        'cash_account_id' => CashAccount::query()
                            ->where('code', config('finance.coa.advance_cash'))
                            ->value('id'),
                        'user_id' => $this->user_id,
                        'paid_at' => $this->cashed_at,
                        'amount' => $amount,
                        'currency_code' => $billCurrency,
                        'exchange_rate' => $exchangeRate,
                    ]);
                    break;
            }
        }
    }

    public function syncJournalEntry(): void
    {
        if (
            $this->is_fixed ||
            $this->category?->group === ExpenseGroup::VendorPayment
        ) {
            $this->journal_entry?->delete();

            return;
        }

        /** @var JournalEntry $entry */
        $entry = $this->journal_entry()->updateOrCreate([], [
            'entry_date' => $this->cashed_at,
            'details' => $this->details,
        ]);
        if ($this->type === 'd') {
            $entry->items()->updateOrCreate([
                'type' => 'd',
            ], [
                'account_id' => CashAccount::query()
                    ->where('code', config('finance.coa.advance_cash'))
                    ->value('id'),
                'amount' => $this->amount * $this->exchange_rate,
                'owner_type' => 'user',
                'owner_id' => $this->user_id,
            ]);
            $entry->items()->updateOrCreate([
                'type' => 'c',
            ], [
                'account_id' => CashAccount::query()
                    ->where('code', config('finance.coa.main_cash'))
                    ->value('id'),
                'amount' => $this->amount * $this->exchange_rate,
            ]);
        } else {
            $accountId = $this->category?->account_id
                ?? CashAccount::query()
                    ->where('code', config('finance.coa.default_expense'))
                    ->value('id');

            $entry->items()->updateOrCreate([
                'type' => 'd',
            ], [
                'account_id' => $accountId,
                'amount' => $this->amount * $this->exchange_rate,
            ]);
            $entry->items()->updateOrCreate([
                'type' => 'c',
            ], [
                'account_id' => CashAccount::query()
                    ->where('code', config('finance.coa.advance_cash'))
                    ->value('id'),
                'amount' => $this->amount * $this->exchange_rate,
                'owner_type' => 'user',
                'owner_id' => $this->user_id,
            ]);
        }
    }

    public function delete()
    {
        $this->journal_entry?->delete();

        return parent::delete();
    }

    public function attachmentUrl(): Attribute
    {
        return new Attribute(
            get: function () {
                if (blank($this->attachment)) {
                    return null;
                }

                return str($this->attachment)->startsWith('http') ? $this->attachment : config('filesystems.disks.s3.url') . $this->attachment;
            },
        );
    }

    public function isVerified(): Attribute
    {
        return Attribute::get(fn () => (bool) $this->verified_at);
    }

    public function verify()
    {
        $this->update([
            'verified_at' => now(),
            'verified_by_id' => auth()->id(),
        ]);
    }
}
