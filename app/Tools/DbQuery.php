<?php

namespace App\Tools;

use App\Services\AiChatService;
use Prism\Prism\Tool;

class Db<PERSON><PERSON>y extends Tool
{
    private AiChatService $aiChatService;

    public function __construct()
    {
        $this->aiChatService = new AiChatService;

        $this
            ->as('db_query')
            ->for('Query the Umrah service database to retrieve information about customers, groups, pilgrims, hotels, flights, invoices, bills, and other business data. Use this when you need to search, analyze, or retrieve specific data from the database.')
            ->withStringParameter('query', 'Natural language query describing what information you need from the database. Be specific about what data you want to retrieve.')
            ->using($this);
    }

    public function __invoke(string $query): string
    {
        try {
            $result = $this->aiChatService->processQuery($query);

            if (! $result['success']) {
                return 'Error processing database query: ' . ($result['message'] ?? 'Unknown error occurred');
            }

            // Format the response for better readability
            $response = $result['response'] ?? 'No response generated';
            $data = $result['data'] ?? [];

            // If we have data, include a summary
            if (! empty($data)) {
                $dataCount = count($data);
                $response .= "\n\n[Query returned {$dataCount} record(s)]";

                // Add a brief data preview if there are results
                if ($dataCount > 0 && $dataCount <= 5) {
                    $response .= "\n\nData preview:\n" . json_encode($data, JSON_PRETTY_PRINT);
                } elseif ($dataCount > 5) {
                    $response .= "\n\nShowing first 3 records:\n" . json_encode(array_slice($data, 0, 3), JSON_PRETTY_PRINT);
                    $response .= "\n... and " . ($dataCount - 3) . ' more records.';
                }
            }

            return $response;

        } catch (\Throwable $e) {
            return 'Database query failed: ' . $e->getMessage();
        }
    }
}
