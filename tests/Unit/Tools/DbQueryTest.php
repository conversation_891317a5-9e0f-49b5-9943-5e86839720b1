<?php

namespace Tests\Unit\Tools;

use App\Tools\DbQuery;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class DbQueryTest extends TestCase
{
    use RefreshDatabase;

    protected DbQuery $dbQuery;

    protected function setUp(): void
    {
        parent::setUp();
        $this->dbQuery = new DbQuery();
    }

    /** @test */
    public function it_can_be_instantiated()
    {
        $this->assertInstanceOf(DbQuery::class, $this->dbQuery);
    }

    /** @test */
    public function it_validates_raw_expressions_correctly()
    {
        // Valid expressions
        $this->assertTrue($this->dbQuery->isValidRawExpression('COUNT(*) as total'));
        $this->assertTrue($this->dbQuery->isValidRawExpression('SUM(amount) as total_amount'));
        $this->assertTrue($this->dbQuery->isValidRawExpression('customers.name as customer_name'));
        $this->assertTrue($this->dbQuery->isValidRawExpression('*'));

        // Invalid expressions (dangerous keywords)
        $this->assertFalse($this->dbQuery->isValidRawExpression('DROP TABLE customers'));
        $this->assertFalse($this->dbQuery->isValidRawExpression('DELETE FROM customers'));
        $this->assertFalse($this->dbQuery->isValidRawExpression('INSERT INTO customers'));
        $this->assertFalse($this->dbQuery->isValidRawExpression('UPDATE customers SET'));
    }

    /** @test */
    public function it_parses_join_conditions_correctly()
    {
        // Mock allowed tables
        config(['ai-chat.allowed_tables' => ['customers', 'groups', 'pilgrims']]);
        $this->dbQuery = new DbQuery(); // Recreate to pick up new config

        // Valid join condition
        $result = $this->dbQuery->parseJoinCondition('customers.id = groups.customer_id');
        $this->assertIsArray($result);
        $this->assertEquals('customers.id', $result['left']);
        $this->assertEquals('=', $result['operator']);
        $this->assertEquals('groups.customer_id', $result['right']);

        // Invalid join condition (missing table.column format)
        $result = $this->dbQuery->parseJoinCondition('id = customer_id');
        $this->assertNull($result);

        // Invalid join condition (table not allowed)
        $result = $this->dbQuery->parseJoinCondition('forbidden_table.id = groups.customer_id');
        $this->assertNull($result);
    }

    /** @test */
    public function it_handles_database_query_execution()
    {
        // Mock the HTTP response for AI query intent extraction
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'content' => json_encode([
                                'table' => 'customers',
                                'fields' => ['id', 'name'],
                                'conditions' => [],
                                'limit' => 10
                            ])
                        ]
                    ]
                ]
            ])
        ]);

        // Create a test customer
        DB::table('customers')->insert([
            'name' => 'Test Customer',
            'email' => '<EMAIL>',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Mock the allowed tables config
        config(['ai-chat.allowed_tables' => ['customers']]);
        
        // Test the tool execution
        $result = $this->dbQuery->__invoke('Show me all customers');
        
        $this->assertIsString($result);
        $this->assertStringContainsString('Test Customer', $result);
    }

    /** @test */
    public function it_rejects_unauthorized_tables()
    {
        // Mock config with limited allowed tables
        config(['ai-chat.allowed_tables' => ['customers']]);
        $this->dbQuery = new DbQuery();

        $queryIntent = [
            'table' => 'unauthorized_table',
            'fields' => ['*'],
            'conditions' => [],
            'limit' => 10
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage("Table 'unauthorized_table' is not allowed for querying");

        $this->dbQuery->executeReadOnlyQuery($queryIntent);
    }

    /** @test */
    public function it_limits_query_results()
    {
        $queryIntent = [
            'table' => 'customers',
            'fields' => ['*'],
            'conditions' => [],
            'limit' => 200 // Try to exceed the 100 limit
        ];

        // Mock allowed tables
        config(['ai-chat.allowed_tables' => ['customers']]);
        $this->dbQuery = new DbQuery();

        // Create multiple test records
        for ($i = 0; $i < 150; $i++) {
            DB::table('customers')->insert([
                'name' => "Customer {$i}",
                'email' => "customer{$i}@example.com",
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        $result = $this->dbQuery->executeReadOnlyQuery($queryIntent);
        
        // Should be limited to 100 records
        $this->assertLessThanOrEqual(100, count($result));
    }

    /** @test */
    public function it_clears_schema_cache()
    {
        // Set some cache data
        Cache::put('ai_chat_schema_info', 'test_data', 3600);
        
        // Verify cache exists
        $this->assertTrue(Cache::has('ai_chat_schema_info'));
        
        // Clear cache
        $this->dbQuery->clearSchemaCache();
        
        // Verify cache is cleared
        $this->assertFalse(Cache::has('ai_chat_schema_info'));
    }
}
